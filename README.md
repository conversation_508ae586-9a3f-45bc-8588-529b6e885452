## Getting Started

First, run the development server:

```bash
npm install
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Deploy on Vercel

Your project is already set up with Vercel, making deployment seamless. Here’s how to manage your workflow effectively:

You can run your project locally in a Vercel-like environment using the vercel dev command. This simulates the production environment, making it easier to catch issues before deployment.

Install Vercel CLI:

```bash
npm install -g vercel
```

Run locally with Vercel dev:

```bash
vercel dev
```

This runs your app locally on [http://localhost:3000](http://localhost:3000). It emulates Vercel’s serverless functions and routing configuration, providing a more accurate local environment than npm run dev.

The latest `main` branch is automatically deployed to Vercel.
